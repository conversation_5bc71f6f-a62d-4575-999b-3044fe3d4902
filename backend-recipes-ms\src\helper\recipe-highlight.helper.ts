/**
 * Helper functions for recipe highlight feature
 * Analyzes RecipeHistory table to determine what was last updated in recipes
 */

import { Op, QueryTypes } from "sequelize";
import { sequelize } from "../models/index";
import { RecipeHistory, RecipeHistoryAction } from "../models/RecipeHistory";
import { getUser } from "./common";
import settingsService from "../services/settings.service";

// Component types that can be highlighted in a recipe
const RecipeComponentType = {
  BASIC_INFO: "basic_info",
  INGREDIENTS: "ingredients",
  STEPS: "steps",
  CATEGORIES: "categories",
  ATTRIBUTES: "attributes",
  RESOURCES: "resources",
  STATUS: "status",
  GENERAL: "general"
};

// Highlight priority levels for different types of changes
const HighlightPriority = {
  LOW: 1,
  MEDIUM: 2,
  HIGH: 3,
  CRITICAL: 4
};

// Mapping of RecipeHistoryAction to component types
const ACTION_TO_COMPONENT_MAP = {
  // Basic recipe information
  [RecipeHistoryAction.created]: RecipeComponentType.STATUS,
  [RecipeHistoryAction.updated]: RecipeComponentType.BASIC_INFO,
  [RecipeHistoryAction.published]: RecipeComponentType.STATUS,
  [RecipeHistoryAction.archived]: RecipeComponentType.STATUS,
  [RecipeHistoryAction.restored]: RecipeComponentType.STATUS,
  [RecipeHistoryAction.deleted]: RecipeComponentType.STATUS,

  // Ingredients
  [RecipeHistoryAction.ingredient_added]: RecipeComponentType.INGREDIENTS,
  [RecipeHistoryAction.ingredient_removed]: RecipeComponentType.INGREDIENTS,
  [RecipeHistoryAction.ingredient_updated]: RecipeComponentType.INGREDIENTS,

  // Steps
  [RecipeHistoryAction.step_added]: RecipeComponentType.STEPS,
  [RecipeHistoryAction.step_removed]: RecipeComponentType.STEPS,
  [RecipeHistoryAction.step_updated]: RecipeComponentType.STEPS,

  // Categories
  [RecipeHistoryAction.category_added]: RecipeComponentType.CATEGORIES,
  [RecipeHistoryAction.category_removed]: RecipeComponentType.CATEGORIES,

  // Attributes
  [RecipeHistoryAction.attribute_added]: RecipeComponentType.ATTRIBUTES,
  [RecipeHistoryAction.attribute_removed]: RecipeComponentType.ATTRIBUTES,

  // Resources
  [RecipeHistoryAction.resource_added]: RecipeComponentType.RESOURCES,
  [RecipeHistoryAction.resource_removed]: RecipeComponentType.RESOURCES,

  // Bookmarks (not highlighted as they're user-specific)
  [RecipeHistoryAction.bookmark_added]: RecipeComponentType.GENERAL,
  [RecipeHistoryAction.bookmark_removed]: RecipeComponentType.GENERAL,
};

// Priority mapping for different actions
const ACTION_PRIORITY_MAP = {
  // High priority - structural changes
  [RecipeHistoryAction.created]: HighlightPriority.HIGH,
  [RecipeHistoryAction.published]: HighlightPriority.HIGH,
  [RecipeHistoryAction.archived]: HighlightPriority.CRITICAL,
  [RecipeHistoryAction.deleted]: HighlightPriority.CRITICAL,
  [RecipeHistoryAction.restored]: HighlightPriority.HIGH,

  // Medium priority - content changes
  [RecipeHistoryAction.updated]: HighlightPriority.MEDIUM,
  [RecipeHistoryAction.ingredient_added]: HighlightPriority.MEDIUM,
  [RecipeHistoryAction.ingredient_removed]: HighlightPriority.MEDIUM,
  [RecipeHistoryAction.ingredient_updated]: HighlightPriority.MEDIUM,
  [RecipeHistoryAction.step_added]: HighlightPriority.MEDIUM,
  [RecipeHistoryAction.step_removed]: HighlightPriority.MEDIUM,
  [RecipeHistoryAction.step_updated]: HighlightPriority.MEDIUM,

  // Low priority - metadata changes
  [RecipeHistoryAction.category_added]: HighlightPriority.LOW,
  [RecipeHistoryAction.category_removed]: HighlightPriority.LOW,
  [RecipeHistoryAction.attribute_added]: HighlightPriority.LOW,
  [RecipeHistoryAction.attribute_removed]: HighlightPriority.LOW,
  [RecipeHistoryAction.resource_added]: HighlightPriority.LOW,
  [RecipeHistoryAction.resource_removed]: HighlightPriority.LOW,

  // Very low priority - user actions
  [RecipeHistoryAction.bookmark_added]: HighlightPriority.LOW,
  [RecipeHistoryAction.bookmark_removed]: HighlightPriority.LOW,
};

// Component display names for UI
const COMPONENT_DISPLAY_NAMES = {
  [RecipeComponentType.BASIC_INFO]: "Basic Information",
  [RecipeComponentType.INGREDIENTS]: "Ingredients",
  [RecipeComponentType.STEPS]: "Preparation Steps",
  [RecipeComponentType.CATEGORIES]: "Categories",
  [RecipeComponentType.ATTRIBUTES]: "Attributes",
  [RecipeComponentType.RESOURCES]: "Resources",
  [RecipeComponentType.STATUS]: "Recipe Status",
  [RecipeComponentType.GENERAL]: "General",
};

// Actions that should be excluded from highlighting (e.g., bookmarks are user-specific)
const EXCLUDED_HIGHLIGHT_ACTIONS = [
  RecipeHistoryAction.bookmark_added,
  RecipeHistoryAction.bookmark_removed,
];

// Default highlight configuration
const DEFAULT_HIGHLIGHT_CONFIG = {
  maxHighlightAgeDays: 30, // Show highlights for changes within last 30 days
  includeUserActions: false, // Don't include bookmarks in highlights
  minPriorityLevel: HighlightPriority.LOW, // Show all priority levels
};

/**
 * Optimized batch user information lookup
 */
const getUserInfoBatch = async (userIds: number[]): Promise<Map<number, any>> => {
  const userInfoMap = new Map();

  if (userIds.length === 0) {
    return userInfoMap;
  }

  try {
    // Use Promise.allSettled for better error handling and parallel processing
    const userPromises = userIds.map(async (userId) => {
      try {
        const userInfo = await getUser(userId);
        return { userId, userInfo, success: true };
      } catch (error) {
        console.warn(`Failed to get user info for user ${userId}:`, error);
        return {
          userId,
          userInfo: { id: userId, user_email: 'Unknown User' },
          success: false
        };
      }
    });

    const results = await Promise.allSettled(userPromises);

    results.forEach((result) => {
      if (result.status === 'fulfilled') {
        const { userId, userInfo } = result.value;
        userInfoMap.set(userId, userInfo);
      } else {
        console.warn('User info promise rejected:', result.reason);
      }
    });

  } catch (error) {
    console.error('Error in batch user lookup:', error);
    // Fallback: create minimal user info for all requested IDs
    userIds.forEach(userId => {
      userInfoMap.set(userId, { id: userId, user_email: 'Unknown User' });
    });
  }

  return userInfoMap;
};

/**
 * Simple cache for organization settings to avoid repeated database calls
 */
const organizationSettingsCache = new Map<string, { settings: any; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Get organization settings with caching
 */
const getCachedOrganizationSettings = async (organizationId: string): Promise<any> => {
  const now = Date.now();
  const cached = organizationSettingsCache.get(organizationId);

  // Check if cache is valid
  if (cached && (now - cached.timestamp) < CACHE_TTL) {
    return cached.settings;
  }

  try {
    // Fetch fresh settings
    const settings = await settingsService.getStructuredSettingsByOrganizationId(organizationId);

    // Cache the result
    organizationSettingsCache.set(organizationId, {
      settings,
      timestamp: now
    });

    return settings;
  } catch (error) {
    console.error("Error fetching organization settings:", error);

    // Return cached value if available, even if expired
    if (cached) {
      console.warn("Using expired cache for organization settings");
      return cached.settings;
    }

    // Fallback to default settings
    return {
      privateRecipeVisibilitySettings: {
        highlightChanges: true // Default to enabled
      }
    };
  }
};



/**
 * Enhanced attribute data formatting with comprehensive support for all attribute types
 */
const formatAttributeData = (attributes: any[], attributeType?: string): any => {
  if (!Array.isArray(attributes)) return attributes;

  return attributes.map(attr => {
    if (typeof attr === 'object' && attr.id) {
      const baseFormat = {
        id: attr.id,
        attribute_title: attr.attribute_title || attr.name || 'Unknown',
        attribute_slug: attr.attribute_slug || attr.slug || 'unknown',
        attribute_type: attr.attribute_type || attr.type || attributeType || 'unknown',
        item_detail: attr.item_detail || {}
      };

      // Add type-specific fields based on attribute type
      switch (attributeType) {
        case 'nutrition':
          return {
            ...baseFormat,
            ...(attr.unit && { unit: attr.unit }),
            ...(attr.unit_of_measure && { unit_of_measure: attr.unit_of_measure }),
            ...(attr.attribute_description && { attribute_description: attr.attribute_description }),
            ...(attr.use_default !== undefined && { use_default: attr.use_default }),
            ...(attr.value !== undefined && { value: attr.value }),
            ...(attr.daily_value_percentage !== undefined && { daily_value_percentage: attr.daily_value_percentage })
          };

        case 'allergen':
          return {
            ...baseFormat,
            ...(attr.may_contain !== undefined && { may_contain: attr.may_contain }),
            ...(attr.contains !== undefined && { contains: attr.contains }),
            ...(attr.severity_level && { severity_level: attr.severity_level }),
            ...(attr.cross_contamination_risk !== undefined && { cross_contamination_risk: attr.cross_contamination_risk })
          };

        case 'haccp':
          return {
            ...baseFormat,
            ...(attr.attribute_description && { attribute_description: attr.attribute_description }),
            ...(attr.use_default !== undefined && { use_default: attr.use_default }),
            ...(attr.control_point_type && { control_point_type: attr.control_point_type }),
            ...(attr.critical_limit && { critical_limit: attr.critical_limit }),
            ...(attr.monitoring_procedure && { monitoring_procedure: attr.monitoring_procedure }),
            ...(attr.corrective_action && { corrective_action: attr.corrective_action })
          };

        case 'dietary':
          return {
            ...baseFormat,
            ...(attr.dietary_restriction_type && { dietary_restriction_type: attr.dietary_restriction_type }),
            ...(attr.compliance_level && { compliance_level: attr.compliance_level }),
            ...(attr.certification_required !== undefined && { certification_required: attr.certification_required })
          };

        case 'cuisine':
          return {
            ...baseFormat,
            ...(attr.region && { region: attr.region }),
            ...(attr.country && { country: attr.country }),
            ...(attr.cuisine_style && { cuisine_style: attr.cuisine_style }),
            ...(attr.traditional_method !== undefined && { traditional_method: attr.traditional_method })
          };

        default:
          // Generic attribute format with all possible fields
          return {
            ...baseFormat,
            ...(attr.unit && { unit: attr.unit }),
            ...(attr.unit_of_measure && { unit_of_measure: attr.unit_of_measure }),
            ...(attr.attribute_description && { attribute_description: attr.attribute_description }),
            ...(attr.use_default !== undefined && { use_default: attr.use_default }),
            ...(attr.may_contain !== undefined && { may_contain: attr.may_contain }),
            ...(attr.value !== undefined && { value: attr.value })
          };
      }
    }

    // Handle legacy format (just IDs)
    return {
      id: attr,
      attribute_title: 'Unknown',
      attribute_slug: 'unknown',
      attribute_type: attributeType || 'unknown',
      item_detail: {}
    };
  });
};

/**
 * Format category data for display - matches get recipe by id structure
 */
const formatCategoryData = (categories: any[]): any => {
  if (!Array.isArray(categories)) return categories;

  return categories.map(cat => {
    if (typeof cat === 'object' && cat.id) {
      return {
        id: cat.id,
        category_name: cat.category_name || cat.name || 'Unknown',
        category_slug: cat.category_slug || cat.slug || 'unknown',
        category_status: cat.category_status || cat.status || 'active',
        item_detail: cat.item_detail || {}
      };
    }
    // Handle legacy format (just IDs)
    return {
      id: cat,
      category_name: 'Unknown',
      category_slug: 'unknown',
      category_status: 'active',
      item_detail: {}
    };
  });
};

/**
 * Format ingredient data for display - matches get recipe by id structure
 */
const formatIngredientData = (ingredients: any[]): any => {
  if (!Array.isArray(ingredients)) return ingredients;

  return ingredients.map(ing => {
    if (typeof ing === 'object' && ing.id) {
      return {
        id: ing.id,
        ingredient_name: ing.ingredient_name || ing.name || 'Unknown',
        ingredient_slug: ing.ingredient_slug || ing.slug || 'unknown',
        ingredient_status: ing.ingredient_status || ing.status || 'unknown',
        ingredient_quantity: ing.ingredient_quantity || ing.quantity,
        ingredient_measure: ing.ingredient_measure || ing.measure,
        ingredient_cost: ing.ingredient_cost || ing.cost,
        ingredient_cooking_method: ing.ingredient_cooking_method || ing.cooking_method,
        preparation_method: ing.preparation_method,
        ingredient_wastage: ing.ingredient_wastage || ing.wastage,
        item_detail: ing.item_detail || {}
      };
    }
    // Handle legacy format (just IDs or basic objects)
    return {
      id: ing.ingredient_id || ing.id || ing,
      ingredient_name: 'Unknown',
      ingredient_slug: 'unknown',
      ingredient_status: 'unknown',
      ingredient_quantity: ing.ingredient_quantity || ing.quantity,
      ingredient_measure: ing.ingredient_measure || ing.measure,
      ingredient_cost: ing.ingredient_cost || ing.cost,
      item_detail: {}
    };
  });
};

/**
 * Format steps data for display - matches get recipe by id structure
 */
const formatStepsData = (steps: any[]): any => {
  if (!Array.isArray(steps)) return steps;

  return steps.map(step => {
    if (typeof step === 'object') {
      return {
        id: step.id,
        recipe_step_order: step.recipe_step_order || step.order || 0,
        recipe_step_description: step.recipe_step_description || step.description || 'No description',
        item_id: step.item_id,
        status: step.status || 'active',
        item_detail: step.item_detail || {}
      };
    }
    // Handle legacy format
    return {
      id: step.id || null,
      recipe_step_order: 0,
      recipe_step_description: 'No description',
      item_id: null,
      status: 'unknown',
      item_detail: {}
    };
  });
};

/**
 * Format resources data for display - matches get recipe by id structure
 */
const formatResourcesData = (resources: any[]): any => {
  if (!Array.isArray(resources)) return resources;

  return resources.map(resource => {
    if (typeof resource === 'object') {
      return {
        id: resource.id,
        type: resource.type || 'item',
        status: resource.status || 'active',
        item_detail: resource.item_detail || {
          item_id: resource.item_id,
          item_name: resource.item_name || 'Unknown File',
          item_type: resource.item_type || 'unknown',
          item_mime_type: resource.item_mime_type || 'unknown',
          item_size: resource.item_size || 0,
          item_location: resource.item_location,
          item_link: resource.item_link
        }
      };
    }
    // Handle legacy format (just IDs)
    return {
      id: resource.id || resource,
      type: 'unknown',
      status: 'unknown',
      item_detail: {
        item_name: 'Unknown',
        item_type: 'unknown'
      }
    };
  });
};

// Helper: Deep compare two objects/arrays, ignoring updated_at and created_at
function deepEqualIgnoreTimestamps(a: any, b: any): boolean {
  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false;
    for (let i = 0; i < a.length; i++) {
      if (!deepEqualIgnoreTimestamps(a[i], b[i])) return false;
    }
    return true;
  }
  if (typeof a === 'object' && typeof b === 'object' && a && b) {
    const aKeys = Object.keys(a).filter(k => k !== 'updated_at' && k !== 'created_at');
    const bKeys = Object.keys(b).filter(k => k !== 'updated_at' && k !== 'created_at');
    if (aKeys.length !== bKeys.length) return false;
    for (const key of aKeys) {
      if (!deepEqualIgnoreTimestamps(a[key], b[key])) return false;
    }
    return true;
  }
  return a === b;
}

/**
 * Get the most recent highlight for a single recipe
 */
export const getRecipeHighlight = async (
  recipeId: number,
  organizationId: string | null,
  config = DEFAULT_HIGHLIGHT_CONFIG
) => {
  try {
    // Build where conditions
    const whereConditions: any = {
      recipe_id: recipeId,
    };

    // Add organization filter only if organizationId is provided
    if (organizationId) {
      whereConditions.organization_id = organizationId;
    }

    // Exclude certain actions if configured
    if (!config.includeUserActions) {
      whereConditions.action = {
        [Op.notIn]: EXCLUDED_HIGHLIGHT_ACTIONS,
      };
    }

    // Get the most recent history entry
    const latestHistory = await RecipeHistory.findOne({
      where: whereConditions,
      order: [["id", "DESC"]],
      limit: 1,
      raw: true,
    });

    if (!latestHistory) {
      return null;
    }

    // Get user information
    const userInfo = await getUser(latestHistory.created_by);

    // Parse old_value and new_value as objects
    let oldObj: Record<string, any> = {};
    let newObj: Record<string, any> = {};

    try {
      oldObj = latestHistory.old_value ? JSON.parse(latestHistory.old_value) : {};
      // Format special fields for better display with type-specific handling
      if (oldObj.dietary_attributes) {
        oldObj.dietary_attributes = formatAttributeData(oldObj.dietary_attributes, 'dietary');
      }
      if (oldObj.nutrition_attributes) {
        oldObj.nutrition_attributes = formatAttributeData(oldObj.nutrition_attributes, 'nutrition');
      }
      if (oldObj.allergen_attributes) {
        oldObj.allergen_attributes = formatAttributeData(oldObj.allergen_attributes, 'allergen');
      }
      if (oldObj.cuisine_attributes) {
        oldObj.cuisine_attributes = formatAttributeData(oldObj.cuisine_attributes, 'cuisine');
      }
      if (oldObj.haccp_attributes) {
        oldObj.haccp_attributes = formatAttributeData(oldObj.haccp_attributes, 'haccp');
      }
      // Handle method flags
      if (oldObj.is_ingredient_cooking_method !== undefined) {
        oldObj.is_ingredient_cooking_method = Boolean(oldObj.is_ingredient_cooking_method);
      }
      if (oldObj.is_preparation_method !== undefined) {
        oldObj.is_preparation_method = Boolean(oldObj.is_preparation_method);
      }
      if (oldObj.categories) {
        oldObj.categories = formatCategoryData(oldObj.categories);
      }
      if (oldObj.ingredients) {
        oldObj.ingredients = formatIngredientData(oldObj.ingredients);
      }
      if (oldObj.steps) {
        oldObj.steps = formatStepsData(oldObj.steps);
      }
      if (oldObj.resources) {
        oldObj.resources = formatResourcesData(oldObj.resources);
      }
    } catch (error: any) {
      console.log("Error parsing old_value:", error);
      oldObj = {};
    }
    try {
      newObj = latestHistory.new_value ? JSON.parse(latestHistory.new_value) : {};
      // Format special fields for better display with type-specific handling
      if (newObj.dietary_attributes) {
        newObj.dietary_attributes = formatAttributeData(newObj.dietary_attributes, 'dietary');
      }
      if (newObj.nutrition_attributes) {
        newObj.nutrition_attributes = formatAttributeData(newObj.nutrition_attributes, 'nutrition');
      }
      if (newObj.allergen_attributes) {
        newObj.allergen_attributes = formatAttributeData(newObj.allergen_attributes, 'allergen');
      }
      if (newObj.cuisine_attributes) {
        newObj.cuisine_attributes = formatAttributeData(newObj.cuisine_attributes, 'cuisine');
      }
      if (newObj.haccp_attributes) {
        newObj.haccp_attributes = formatAttributeData(newObj.haccp_attributes, 'haccp');
      }
      // Handle method flags
      if (newObj.is_ingredient_cooking_method !== undefined) {
        newObj.is_ingredient_cooking_method = Boolean(newObj.is_ingredient_cooking_method);
      }
      if (newObj.is_preparation_method !== undefined) {
        newObj.is_preparation_method = Boolean(newObj.is_preparation_method);
      }
      if (newObj.categories) {
        newObj.categories = formatCategoryData(newObj.categories);
      }
      if (newObj.ingredients) {
        newObj.ingredients = formatIngredientData(newObj.ingredients);
      }
      if (newObj.steps) {
        newObj.steps = formatStepsData(newObj.steps);
      }
      if (newObj.resources) {
        newObj.resources = formatResourcesData(newObj.resources);
      }
    } catch (error: any) {
      console.log("Error parsing new_value:", error);
      newObj = {};
    }

    // Build highlight object mapping field names to old_value
    const highlightObject: Record<string, any> = {};

    // Only consider fields that were actually changed in the request (present in newObj)
    // This prevents showing old nutrition/allergen data when only ingredients were updated
    const changedFields = Object.keys(newObj);

    const filteredFields = changedFields
      .filter(field => {
        // Only include if old_value !== new_value
        const oldVal = oldObj[field];
        const newVal = newObj[field];

        // Normalize boolean fields for comparison
        const booleanFields = [
          'is_ingredient_cooking_method',
          'is_preparation_method',
          'is_cost_manual',
          'has_recipe_public_visibility',
          'has_recipe_private_visibility',
        ];
        if (booleanFields.includes(field)) {
          const result = Boolean(oldVal) !== Boolean(newVal);
          return result;
        }
        // For objects/arrays, do a deep comparison ignoring timestamps
        if (typeof oldVal === 'object' || typeof newVal === 'object') {
          const result = !deepEqualIgnoreTimestamps(oldVal, newVal);
          return result;
        }
        const result = oldVal !== newVal;
        return result;
      });

    filteredFields.forEach(field => {
      highlightObject[field] = oldObj[field];
    });

    // Return highlight object and metadata
    return {
      highlight: highlightObject,
      lastModified: latestHistory.created_at,
      modifiedBy: {
        userId: latestHistory.created_by,
        userName: userInfo?.user_full_name || userInfo?.user_first_name || userInfo?.user_email || "Unknown User",
      },
    };
  } catch (error) {
    console.error("Error getting recipe highlight:", error);
    return null;
  }
};

/**
 * Get highlights for multiple recipes efficiently
 */
export const getBulkRecipeHighlights = async (
  recipeIds: number[],
  organizationId: string | null,
  config = DEFAULT_HIGHLIGHT_CONFIG
) => {
  try {
    if (!recipeIds || recipeIds.length === 0) {
      return {
        highlights: {},
        metadata: {
          totalRecipes: 0,
          recipesWithHighlights: 0,
          queryTimestamp: new Date(),
        },
      };
    }

    // Performance optimization: limit batch size for large datasets
    const MAX_BATCH_SIZE = 1000;
    if (recipeIds.length > MAX_BATCH_SIZE) {
      console.warn(`Large highlight batch detected (${recipeIds.length} recipes), processing in chunks`);

      // Process in chunks and combine results
      const chunks = [];
      for (let i = 0; i < recipeIds.length; i += MAX_BATCH_SIZE) {
        chunks.push(recipeIds.slice(i, i + MAX_BATCH_SIZE));
      }

      const chunkResults = await Promise.all(
        chunks.map(chunk => getBulkRecipeHighlights(chunk, organizationId, config))
      );

      // Combine results
      const combinedHighlights = {};
      let totalWithHighlights = 0;

      chunkResults.forEach(result => {
        Object.assign(combinedHighlights, result.highlights);
        totalWithHighlights += result.metadata.recipesWithHighlights;
      });

      return {
        highlights: combinedHighlights,
        metadata: {
          totalRecipes: recipeIds.length,
          recipesWithHighlights: totalWithHighlights,
          queryTimestamp: new Date(),
          processedInChunks: true,
          chunkCount: chunks.length,
        },
      };
    }

    // Check organization settings to see if highlights are enabled (with caching)
    if (organizationId) {
      const organizationSettings = await getCachedOrganizationSettings(organizationId);
      const isHighlightedAllowed = organizationSettings.privateRecipeVisibilitySettings.highlightChanges;

      if (!isHighlightedAllowed) {
        return {
          highlights: {},
          metadata: {
            totalRecipes: recipeIds.length,
            recipesWithHighlights: 0,
            queryTimestamp: new Date(),
            highlightsDisabled: true,
          },
        };
      }
    }

    // Calculate the cutoff date for highlights
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - config.maxHighlightAgeDays);

    // Build the SQL query to get the most recent history entry for each recipe
    const excludedActions = config.includeUserActions ? [] : EXCLUDED_HIGHLIGHT_ACTIONS;
    const excludedActionsClause = excludedActions.length > 0
      ? `AND rh.action NOT IN (${excludedActions.map(a => `'${a}'`).join(', ')})`
      : '';

    const organizationClause = organizationId
      ? `AND rh.organization_id = :organizationId`
      : '';

    // Optimized query with better indexing and reduced data transfer
    const query = `
      SELECT
        rh.recipe_id,
        rh.action,
        rh.description,
        rh.field_name,
        rh.old_value,
        rh.created_at,
        rh.created_by
      FROM mo_recipe_history rh
      INNER JOIN (
        SELECT
          recipe_id,
          MAX(created_at) as max_created_at
        FROM mo_recipe_history
        WHERE recipe_id IN (${recipeIds.map((id: number) => `${id}`).join(', ')})
          AND created_at >= :cutoffDate
          ${excludedActionsClause}
          ${organizationClause}
        GROUP BY recipe_id
      ) latest ON rh.recipe_id = latest.recipe_id AND rh.created_at = latest.max_created_at
      WHERE rh.recipe_id IN (${recipeIds.map((id: number) => `${id}`).join(', ')})
        AND rh.created_at >= :cutoffDate
        ${excludedActionsClause}
        ${organizationClause}
      ORDER BY rh.recipe_id, rh.created_at DESC
    `;

    const results = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      replacements: {
        cutoffDate: cutoffDate.toISOString(),
        ...(organizationId && { organizationId }),
      },
    });

    // Apply priority filter (no need to filter by rn since query already gets latest)
    const latestEntries = results.filter((row: any) => {
      const actionPriority = (ACTION_PRIORITY_MAP as any)[row.action];
      return actionPriority >= config.minPriorityLevel;
    });

    // Optimized user information lookup with batch query
    const userIds: any = [...new Set(latestEntries.map((entry: any) => Number(entry.created_by)).filter((id: any) => !isNaN(id)))];
    const userInfoMap = await getUserInfoBatch(userIds);

    // Build highlights map
    const highlights: any = {};

    for (const entry of latestEntries) {
      const userInfo = userInfoMap.get(Number(entry.created_by));
      const component = (ACTION_TO_COMPONENT_MAP as any)[entry.action];
      const actionPriority = (ACTION_PRIORITY_MAP as any)[entry.action];

      // Parse old_value if it exists and extract only relevant part
      let oldData = null;
      if (entry.old_value) {
        try {
          const fullOldValue = JSON.parse(entry.old_value);
          // Format special fields for better display with type-specific handling
          if (fullOldValue.dietary_attributes) {
            fullOldValue.dietary_attributes = formatAttributeData(fullOldValue.dietary_attributes, 'dietary');
          }
          if (fullOldValue.nutrition_attributes) {
            fullOldValue.nutrition_attributes = formatAttributeData(fullOldValue.nutrition_attributes, 'nutrition');
          }
          if (fullOldValue.allergen_attributes) {
            fullOldValue.allergen_attributes = formatAttributeData(fullOldValue.allergen_attributes, 'allergen');
          }
          if (fullOldValue.cuisine_attributes) {
            fullOldValue.cuisine_attributes = formatAttributeData(fullOldValue.cuisine_attributes, 'cuisine');
          }
          if (fullOldValue.haccp_attributes) {
            fullOldValue.haccp_attributes = formatAttributeData(fullOldValue.haccp_attributes, 'haccp');
          }
          if (fullOldValue.categories) {
            fullOldValue.categories = formatCategoryData(fullOldValue.categories);
          }
          if (fullOldValue.ingredients) {
            fullOldValue.ingredients = formatIngredientData(fullOldValue.ingredients);
          }
          if (fullOldValue.steps) {
            fullOldValue.steps = formatStepsData(fullOldValue.steps);
          }
          if (fullOldValue.resources) {
            fullOldValue.resources = formatResourcesData(fullOldValue.resources);
          }
          oldData = extractRelevantOldData(component, fullOldValue, entry.field_name);
        } catch {
          // If it's not JSON, return as string
          oldData = entry.old_value;
        }
      }

      highlights[entry.recipe_id] = {
        component,
        action: entry.action,
        description: entry.description || generateDefaultDescription(entry.action, component),
        lastModified: new Date(entry.created_at),
        old_value: oldData,
        modifiedBy: {
          userId: entry.created_by,
          userName: userInfo?.user_full_name || userInfo?.user_first_name || userInfo?.user_email || "Unknown User",
        },
        priority: actionPriority,
        context: {
          fieldName: entry.field_name,
        },
      };
    }

    return {
      highlights,
      metadata: {
        totalRecipes: recipeIds.length,
        recipesWithHighlights: Object.keys(highlights).length,
        queryTimestamp: new Date(),
      },
    };
  } catch (error) {
    console.error("Error getting bulk recipe highlights:", error);
    return {
      highlights: {},
      metadata: {
        totalRecipes: recipeIds.length,
        recipesWithHighlights: 0,
        queryTimestamp: new Date(),
      },
    };
  }
};

/**
 * Extract only the relevant old data for the specific component that was updated
 * Now accepts field_name to extract only the specific field that was changed
 */
function extractRelevantOldData(component: string, oldValue: any, fieldName?: string): any {
  if (!oldValue || typeof oldValue !== 'object') {
    return oldValue;
  }

  // If field_name is provided, extract only that specific field
  if (fieldName) {
    // Special handling for batch_update - only return fields that were actually changed
    if (fieldName === 'batch_update') {
      // For batch updates, we need to be more selective about what we return
      // Only return the component-specific data, not all old data
      switch (component) {
        case RecipeComponentType.INGREDIENTS:
          return oldValue.ingredients || null;
        case RecipeComponentType.ATTRIBUTES:
          // For attributes, only return the specific attribute types that were changed
          // This prevents showing old nutrition/allergen data when only ingredients were updated
          return null; // Let the main highlight logic handle this
        case RecipeComponentType.BASIC_INFO:
          return oldValue;
        default:
          return oldValue[fieldName] !== undefined ? oldValue[fieldName] : null;
      }
    }

    // For basic_info component, check if the field_name exists in the old value
    if (component === RecipeComponentType.BASIC_INFO) {
      return oldValue[fieldName] !== undefined ? { [fieldName]: oldValue[fieldName] } : null;
    }

    // For other components, return the specific field if it exists
    return oldValue[fieldName] !== undefined ? oldValue[fieldName] : null;
  }

  // Fallback to the original logic if no field_name is provided
  switch (component) {
    case RecipeComponentType.INGREDIENTS:
      return oldValue.ingredients || null;

    case RecipeComponentType.STEPS:
      return oldValue.steps || null;

    case RecipeComponentType.CATEGORIES:
      return oldValue.categories || null;

    case RecipeComponentType.ATTRIBUTES: {
      // Return all attribute types with enhanced formatting
      const attributeData: any = {};

      if (oldValue.nutrition_attributes) {
        attributeData.nutrition_attributes = formatAttributeData(oldValue.nutrition_attributes, 'nutrition');
      }
      if (oldValue.allergen_attributes) {
        attributeData.allergen_attributes = formatAttributeData(oldValue.allergen_attributes, 'allergen');
      }
      if (oldValue.cuisine_attributes) {
        attributeData.cuisine_attributes = formatAttributeData(oldValue.cuisine_attributes, 'cuisine');
      }
      if (oldValue.dietary_attributes) {
        attributeData.dietary_attributes = formatAttributeData(oldValue.dietary_attributes, 'dietary');
      }
      if (oldValue.haccp_attributes) {
        attributeData.haccp_attributes = formatAttributeData(oldValue.haccp_attributes, 'haccp');
      }
      if (oldValue.attributes) {
        attributeData.attributes = formatAttributeData(oldValue.attributes); // Legacy support
      }

      // Return null if no attributes found, otherwise return the formatted data
      return Object.keys(attributeData).length > 0 ? attributeData : null;
    }

    case RecipeComponentType.RESOURCES:
      return oldValue.resources || null;

    case RecipeComponentType.BASIC_INFO:
      // Return basic recipe information fields
      return {
        recipe_title: oldValue.recipe_title,
        recipe_description: oldValue.recipe_description,
        recipe_preparation_time: oldValue.recipe_preparation_time,
        recipe_cook_time: oldValue.recipe_cook_time,
        recipe_yield: oldValue.recipe_yield,
        recipe_yield_unit: oldValue.recipe_yield_unit,
        recipe_total_portions: oldValue.recipe_total_portions,
        recipe_single_portion_size: oldValue.recipe_single_portion_size,
        recipe_serving_method: oldValue.recipe_serving_method,
        recipe_complexity_level: oldValue.recipe_complexity_level,
        vitamin_a: oldValue.vitamin_a,
        vitamin_c: oldValue.vitamin_c,
        calcium: oldValue.calcium,
        iron: oldValue.iron,
        is_ingredient_cooking_method: oldValue.is_ingredient_cooking_method,
        is_preparation_method: oldValue.is_preparation_method
      };

    case RecipeComponentType.STATUS:
      return {
        recipe_status: oldValue.recipe_status,
        has_recipe_public_visibility: oldValue.has_recipe_public_visibility,
        has_recipe_private_visibility: oldValue.has_recipe_private_visibility
      };

    default:
      // For unknown components, return the entire old value
      return oldValue;
  }
}

/**
 * Get simplified recipe highlight - returns only the updated data in a single key
 * For FE team requirement: only updated data, no extra metadata
 */
export const getSimpleRecipeHighlight = async (
  recipeId: number,
  organizationId: string | null,
  config = DEFAULT_HIGHLIGHT_CONFIG
) => {
  try {
    // Calculate the cutoff date for highlights
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - config.maxHighlightAgeDays);

    // Build where conditions
    const whereConditions: any = {
      recipe_id: recipeId,
      created_at: {
        [Op.gte]: cutoffDate,
      },
    };

    if (organizationId) {
      whereConditions.organization_id = organizationId;
    }

    // Exclude certain actions if configured
    if (!config.includeUserActions) {
      whereConditions.action = {
        [Op.notIn]: EXCLUDED_HIGHLIGHT_ACTIONS,
      };
    }

    // Get the most recent history entry
    const latestHistory = await RecipeHistory.findOne({
      where: whereConditions,
      order: [["created_at", "DESC"]],
      limit: 1,
    });

    if (!latestHistory) {
      return { updated: null };
    }

    // Check if the action meets minimum priority level
    const actionPriority = (ACTION_PRIORITY_MAP as any)[latestHistory.action];
    if (actionPriority < config.minPriorityLevel) {
      return { updated: null };
    }

    // Map action to component type
    const component = (ACTION_TO_COMPONENT_MAP as any)[latestHistory.action];

    // Parse old_value if it exists and extract only relevant part
    let oldData = null;
    if (latestHistory.old_value) {
      try {
        const fullOldValue = JSON.parse(latestHistory.old_value);
        oldData = extractRelevantOldData(component, fullOldValue, latestHistory.field_name);
      } catch {
        // If it's not JSON, return as string
        oldData = latestHistory.old_value;
      }
    }

    // Return only the updated component type and relevant old data
    return {
      updated: component,
      old_data: oldData
    };
  } catch (error) {
    console.error("Error getting simple recipe highlight:", error);
    return { updated: null };
  }
};

/**
 * Get simplified bulk recipe highlights - returns only updated components and relevant old data for multiple recipes
 * For FE team requirement: only updated data, no extra metadata
 */
export const getSimpleBulkRecipeHighlights = async (
  recipeIds: number[],
  organizationId: string | null,
  config = DEFAULT_HIGHLIGHT_CONFIG
) => {
  try {
    if (recipeIds.length === 0) {
      return {};
    }

    // Calculate the cutoff date for highlights
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - config.maxHighlightAgeDays);

    // Build the SQL query to get the most recent history entry for each recipe
    const excludedActions = config.includeUserActions ? [] : EXCLUDED_HIGHLIGHT_ACTIONS;
    const excludedActionsClause = excludedActions.length > 0
      ? `AND rh.action NOT IN (${excludedActions.map(a => `'${a}'`).join(', ')})`
      : '';

    const organizationClause = organizationId
      ? `AND rh.organization_id = :organizationId`
      : '';

    const query = `
      SELECT 
        rh.recipe_id,
        rh.action,
        rh.old_value,
        rh.field_name,
        ROW_NUMBER() OVER (PARTITION BY rh.recipe_id ORDER BY rh.created_at DESC) as rn
      FROM mo_recipe_history rh
      WHERE rh.recipe_id IN (${recipeIds.map((id: number) => `${id}`).join(', ')})
        AND rh.created_at >= :cutoffDate
        ${excludedActionsClause}
        ${organizationClause}
    `;

    const results = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      replacements: {
        cutoffDate: cutoffDate.toISOString(),
        ...(organizationId && { organizationId }),
      },
    });

    // Filter to get only the most recent entry per recipe and apply priority filter
    const latestEntries = results.filter((row: any) => {
      if (row.rn !== 1) return false; // Only the most recent entry

      const actionPriority = (ACTION_PRIORITY_MAP as any)[row.action];
      return actionPriority >= config.minPriorityLevel;
    });

    // Build simple highlights map with only the updated component and relevant old data
    const highlights: any = {};

    for (const entry of latestEntries) {
      const component = (ACTION_TO_COMPONENT_MAP as any)[entry.action];

      // Parse old_value if it exists and extract only relevant part
      let oldData = null;
      if (entry.old_value) {
        try {
          const fullOldValue = JSON.parse(entry.old_value);
          // Format special fields for better display with type-specific handling
          if (fullOldValue.dietary_attributes) {
            fullOldValue.dietary_attributes = formatAttributeData(fullOldValue.dietary_attributes, 'dietary');
          }
          if (fullOldValue.nutrition_attributes) {
            fullOldValue.nutrition_attributes = formatAttributeData(fullOldValue.nutrition_attributes, 'nutrition');
          }
          if (fullOldValue.allergen_attributes) {
            fullOldValue.allergen_attributes = formatAttributeData(fullOldValue.allergen_attributes, 'allergen');
          }
          if (fullOldValue.cuisine_attributes) {
            fullOldValue.cuisine_attributes = formatAttributeData(fullOldValue.cuisine_attributes, 'cuisine');
          }
          if (fullOldValue.haccp_attributes) {
            fullOldValue.haccp_attributes = formatAttributeData(fullOldValue.haccp_attributes, 'haccp');
          }
          if (fullOldValue.categories) {
            fullOldValue.categories = formatCategoryData(fullOldValue.categories);
          }
          if (fullOldValue.ingredients) {
            fullOldValue.ingredients = formatIngredientData(fullOldValue.ingredients);
          }
          if (fullOldValue.steps) {
            fullOldValue.steps = formatStepsData(fullOldValue.steps);
          }
          if (fullOldValue.resources) {
            fullOldValue.resources = formatResourcesData(fullOldValue.resources);
          }
          oldData = extractRelevantOldData(component, fullOldValue, entry.field_name);
        } catch {
          // If it's not JSON, return as string
          oldData = entry.old_value;
        }
      }

      highlights[entry.recipe_id] = {
        updated: component,
        old_data: oldData
      };
    }

    return highlights;
  } catch (error) {
    console.error("Error getting simple bulk recipe highlights:", error);
    return {};
  }
};

/**
 * Generate a default description for an action if none exists
 */
function generateDefaultDescription(action: string, component: string) {
  const componentName = COMPONENT_DISPLAY_NAMES[component];

  switch (action) {
    case RecipeHistoryAction.created:
      return "Recipe was created";
    case RecipeHistoryAction.updated:
      return `${componentName} was updated`;
    case RecipeHistoryAction.published:
      return "Recipe was published";
    case RecipeHistoryAction.archived:
      return "Recipe was archived";
    case RecipeHistoryAction.restored:
      return "Recipe was restored";
    case RecipeHistoryAction.deleted:
      return "Recipe was deleted";
    case RecipeHistoryAction.ingredient_added:
      return "New ingredient was added";
    case RecipeHistoryAction.ingredient_removed:
      return "Ingredient was removed";
    case RecipeHistoryAction.ingredient_updated:
      return "Ingredient was updated";
    case RecipeHistoryAction.step_added:
      return "New preparation step was added";
    case RecipeHistoryAction.step_removed:
      return "Preparation step was removed";
    case RecipeHistoryAction.step_updated:
      return "Preparation step was updated";
    case RecipeHistoryAction.category_added:
      return "Category was added";
    case RecipeHistoryAction.category_removed:
      return "Category was removed";
    case RecipeHistoryAction.attribute_added:
      return "Attribute was added";
    case RecipeHistoryAction.attribute_removed:
      return "Attribute was removed";
    case RecipeHistoryAction.resource_added:
      return "Resource was added";
    case RecipeHistoryAction.resource_removed:
      return "Resource was removed";
    default:
      return `${componentName} was modified`;
  }
}

/**
 * Check if a recipe has recent changes within the specified timeframe
 */
export const hasRecentChanges = async (
  recipeId: number,
  organizationId: string | null,
  daysBack: number = 7
): Promise<boolean> => {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysBack);

    const whereConditions: any = {
      recipe_id: recipeId,
      created_at: {
        [Op.gte]: cutoffDate,
      },
      action: {
        [Op.notIn]: EXCLUDED_HIGHLIGHT_ACTIONS,
      },
    };

    if (organizationId) {
      whereConditions.organization_id = organizationId;
    }

    const count = await RecipeHistory.count({
      where: whereConditions,
    });

    return count > 0;
  } catch (error) {
    console.error("Error checking recent changes:", error);
    return false;
  }
};
